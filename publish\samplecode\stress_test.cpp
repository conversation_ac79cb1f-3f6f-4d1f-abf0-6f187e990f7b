/**
 * @file stress_test.cpp
 * @brief Stress testing program for ARC Common Logger SDK
 * 
 * This program performs stress testing to verify stability and reliability:
 * - High-frequency logging from multiple threads
 * - Memory leak detection
 * - File rotation under heavy load
 * - Error handling under extreme conditions
 * - Long-running stability test
 */

#include "../sdk/inc/arc_common_logger.h"
#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <random>
#include <signal.h>
#include <sys/resource.h>
#include <unistd.h>

class StressTest {
private:
    std::atomic<bool> running{true};
    std::atomic<long> total_messages{0};
    std::atomic<long> error_count{0};
    std::mt19937 rng;
    std::uniform_int_distribution<int> level_dist;
    std::uniform_int_distribution<int> size_dist;
    
public:
    StressTest() : rng(std::random_device{}()), 
                   level_dist(ARC_LOG_TRACE, ARC_LOG_CRITICAL),
                   size_dist(50, 2000) {}
    
    // Signal handler for graceful shutdown
    static void signalHandler(int signal) {
        std::cout << "\nReceived signal " << signal << ", shutting down gracefully..." << std::endl;
        // Note: In a real implementation, you'd use a global instance or static member
    }
    
    void stop() {
        running = false;
    }
    
    // Get memory usage in KB
    long getMemoryUsage() {
        struct rusage usage;
        getrusage(RUSAGE_SELF, &usage);
        return usage.ru_maxrss;
    }
    
    // Generate random message
    std::string generateRandomMessage() {
        size_t size = size_dist(rng);
        std::string msg = "Stress test message ";
        
        while (msg.length() < size - 20) {
            msg += std::to_string(rng()) + " ";
        }
        
        return msg;
    }
    
    // Worker thread function
    void workerThread(int thread_id, int target_messages_per_second) {
        std::cout << "Worker thread " << thread_id << " started (target: " 
                  << target_messages_per_second << " msg/s)" << std::endl;
        
        auto interval = std::chrono::microseconds(1000000 / target_messages_per_second);
        auto next_time = std::chrono::steady_clock::now();
        
        long local_count = 0;
        
        while (running) {
            try {
                std::string msg = generateRandomMessage();
                arc_log_level_t level = static_cast<arc_log_level_t>(level_dist(rng));
                
                // Log with random level
                switch (level) {
                    case ARC_LOG_TRACE:
                        arc_log_trace("Thread %d: %s", thread_id, msg.c_str());
                        break;
                    case ARC_LOG_DEBUG:
                        arc_log_debug("Thread %d: %s", thread_id, msg.c_str());
                        break;
                    case ARC_LOG_INFO:
                        arc_log_info("Thread %d: %s", thread_id, msg.c_str());
                        break;
                    case ARC_LOG_WARN:
                        arc_log_warn("Thread %d: %s", thread_id, msg.c_str());
                        break;
                    case ARC_LOG_ERROR:
                        arc_log_error("Thread %d: %s", thread_id, msg.c_str());
                        break;
                    case ARC_LOG_CRITICAL:
                        arc_log_critical("Thread %d: %s", thread_id, msg.c_str());
                        break;
                    default:
                        arc_log_info("Thread %d: %s", thread_id, msg.c_str());
                        break;
                }
                
                local_count++;
                total_messages++;
                
                // Rate limiting
                next_time += interval;
                std::this_thread::sleep_until(next_time);
                
            } catch (...) {
                error_count++;
                std::cerr << "Error in worker thread " << thread_id << std::endl;
            }
        }
        
        std::cout << "Worker thread " << thread_id << " completed (" 
                  << local_count << " messages)" << std::endl;
    }
    
    // High-frequency burst test
    void burstTest(int num_threads, int burst_duration_seconds, int messages_per_second_per_thread) {
        std::cout << "\n=== Burst Test ===" << std::endl;
        std::cout << "Threads: " << num_threads << std::endl;
        std::cout << "Duration: " << burst_duration_seconds << " seconds" << std::endl;
        std::cout << "Rate: " << messages_per_second_per_thread << " msg/s per thread" << std::endl;
        std::cout << "Total expected: " << num_threads * messages_per_second_per_thread * burst_duration_seconds << " messages" << std::endl;
        
        arc_logger_config_t config;
        arc_logger_get_default_config(&config);
        config.log_file_path = "stress_burst.log";
        config.max_file_size = 50 * 1024 * 1024; // 50MB files
        config.max_files = 10;
        config.async_mode = 1; // Use async for high performance
        config.async_queue_size = 65536;
        config.async_thread_count = 4;
        config.level = ARC_LOG_TRACE; // Log everything
        config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v";
        
        if (arc_logger_init(&config) != ARC_LOG_SUCCESS) {
            std::cerr << "Failed to initialize logger for burst test" << std::endl;
            return;
        }
        
        running = true;
        total_messages = 0;
        error_count = 0;
        
        long memory_start = getMemoryUsage();
        auto start_time = std::chrono::steady_clock::now();
        
        // Start worker threads
        std::vector<std::thread> threads;
        for (int i = 0; i < num_threads; ++i) {
            threads.emplace_back(&StressTest::workerThread, this, i, messages_per_second_per_thread);
        }
        
        // Monitor progress
        for (int i = 0; i < burst_duration_seconds; ++i) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            long current_messages = total_messages;
            long current_memory = getMemoryUsage();
            
            std::cout << "Progress: " << (i + 1) << "s - Messages: " << current_messages 
                      << ", Memory: " << (current_memory - memory_start) << " KB, Errors: " << error_count << std::endl;
        }
        
        // Stop all threads
        running = false;
        for (auto& thread : threads) {
            thread.join();
        }
        
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        long memory_peak = getMemoryUsage();
        
        // Wait for async processing
        std::this_thread::sleep_for(std::chrono::seconds(2));
        arc_logger_flush();
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        std::cout << "\nBurst Test Results:" << std::endl;
        std::cout << "Duration: " << duration.count() << " ms" << std::endl;
        std::cout << "Total messages: " << total_messages << std::endl;
        std::cout << "Messages/second: " << (double)total_messages / (duration.count() / 1000.0) << std::endl;
        std::cout << "Errors: " << error_count << std::endl;
        std::cout << "Memory usage: " << (memory_peak - memory_start) << " KB" << std::endl;
        
        arc_logger_shutdown();
    }
    
    // Long-running stability test
    void stabilityTest(int duration_minutes, int num_threads, int base_rate) {
        std::cout << "\n=== Stability Test ===" << std::endl;
        std::cout << "Duration: " << duration_minutes << " minutes" << std::endl;
        std::cout << "Threads: " << num_threads << std::endl;
        std::cout << "Base rate: " << base_rate << " msg/s per thread" << std::endl;
        
        arc_logger_config_t config;
        arc_logger_get_default_config(&config);
        config.log_file_path = "stress_stability.log";
        config.max_file_size = 20 * 1024 * 1024; // 20MB files for frequent rotation
        config.max_files = 20;
        config.async_mode = 1;
        config.async_queue_size = 32768;
        config.async_thread_count = 3;
        config.level = ARC_LOG_DEBUG;
        config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v";
        
        if (arc_logger_init(&config) != ARC_LOG_SUCCESS) {
            std::cerr << "Failed to initialize logger for stability test" << std::endl;
            return;
        }
        
        running = true;
        total_messages = 0;
        error_count = 0;
        
        long memory_start = getMemoryUsage();
        auto start_time = std::chrono::steady_clock::now();
        
        // Start worker threads with varying rates
        std::vector<std::thread> threads;
        for (int i = 0; i < num_threads; ++i) {
            int thread_rate = base_rate + (i % 3) * 10; // Vary the rate slightly
            threads.emplace_back(&StressTest::workerThread, this, i, thread_rate);
        }
        
        // Monitor for the specified duration
        int total_seconds = duration_minutes * 60;
        long last_message_count = 0;
        
        for (int i = 0; i < total_seconds; i += 10) {
            std::this_thread::sleep_for(std::chrono::seconds(10));
            
            long current_messages = total_messages;
            long current_memory = getMemoryUsage();
            long messages_in_interval = current_messages - last_message_count;
            last_message_count = current_messages;
            
            double rate = messages_in_interval / 10.0;
            
            std::cout << "Time: " << (i + 10) << "s/" << total_seconds << "s - "
                      << "Messages: " << current_messages << " (+" << messages_in_interval << ") - "
                      << "Rate: " << rate << " msg/s - "
                      << "Memory: " << (current_memory - memory_start) << " KB - "
                      << "Errors: " << error_count << std::endl;
            
            // Force periodic flush to test stability
            if ((i + 10) % 60 == 0) {
                arc_logger_flush();
            }
        }
        
        // Stop all threads
        running = false;
        for (auto& thread : threads) {
            thread.join();
        }
        
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);
        long memory_peak = getMemoryUsage();
        
        // Final flush and wait
        arc_logger_flush();
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        std::cout << "\nStability Test Results:" << std::endl;
        std::cout << "Actual duration: " << duration.count() << " seconds" << std::endl;
        std::cout << "Total messages: " << total_messages << std::endl;
        std::cout << "Average rate: " << (double)total_messages / duration.count() << " msg/s" << std::endl;
        std::cout << "Errors: " << error_count << std::endl;
        std::cout << "Peak memory usage: " << (memory_peak - memory_start) << " KB" << std::endl;
        
        if (error_count == 0) {
            std::cout << "✓ Stability test PASSED - No errors detected" << std::endl;
        } else {
            std::cout << "✗ Stability test FAILED - " << error_count << " errors detected" << std::endl;
        }
        
        arc_logger_shutdown();
    }
};

int main(int argc, char* argv[]) {
    std::cout << "ARC Common Logger SDK - Stress Test" << std::endl;
    std::cout << "====================================" << std::endl;
    
    // Setup signal handler
    signal(SIGINT, StressTest::signalHandler);
    signal(SIGTERM, StressTest::signalHandler);
    
    StressTest test;
    
    try {
        // Quick burst test
        test.burstTest(4, 30, 100);  // 4 threads, 30 seconds, 100 msg/s each
        
        std::this_thread::sleep_for(std::chrono::seconds(5)); // Cool down
        
        // High-intensity burst test
        test.burstTest(8, 60, 200);  // 8 threads, 60 seconds, 200 msg/s each
        
        std::this_thread::sleep_for(std::chrono::seconds(5)); // Cool down
        
        // Long-running stability test (shorter for demo)
        test.stabilityTest(5, 6, 50); // 5 minutes, 6 threads, 50 msg/s each
        
        std::cout << "\n" << std::string(50, '=') << std::endl;
        std::cout << "All stress tests completed successfully!" << std::endl;
        std::cout << "Check the generated log files:" << std::endl;
        std::cout << "  - stress_burst.log" << std::endl;
        std::cout << "  - stress_stability.log" << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Stress test failed: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
