# ARC Common Logger SDK Makefile
TARGET_NAME = libarc_common_logger

# Compiler settings
CROSS_COMPILE = aarch64-none-linux-gnu-
CC = $(CROSS_COMPILE)gcc
CXX = $(CROSS_COMPILE)g++
STRIP = $(CROSS_COMPILE)strip
AR = $(CROSS_COMPILE)ar
LD = $(CROSS_COMPILE)ld

# Directories
SPDLOG_DIR = ../Related_Project/spdlog
INC_DIR = inc
SRC_DIR = src
BUILD_DIR = ../build
LIB_DIR = lib

# Include paths
INCLUDES = -I$(INC_DIR) -I$(SPDLOG_DIR)/include

# Library paths
LIBPATHS = -L$(SPDLOG_DIR)/lib

# Libraries to link
LIBS = -lspdlog -lpthread

# Compiler flags
CXXFLAGS = -std=c++11 -Wall -Wextra -O2 -fPIC $(INCLUDES)
CFLAGS = -std=c99 -Wall -Wextra -O2 -fPIC $(INCLUDES)

# Debug flags (uncomment for debug build)
ifeq ($(COMPILE_MODEL), debug)
	CXXFLAGS += -g -DDEBUG
	CFLAGS += -g -DDEBUG
endif

# Source files
CPP_SOURCES = $(wildcard $(SRC_DIR)/*.cpp)
C_SOURCES = $(wildcard $(SRC_DIR)/*.c)

# Object files
CPP_OBJECTS = $(CPP_SOURCES:$(SRC_DIR)/%.cpp=$(BUILD_DIR)/%.o)
C_OBJECTS = $(C_SOURCES:$(SRC_DIR)/%.c=$(BUILD_DIR)/%.o)
OBJECTS = $(CPP_OBJECTS) $(C_OBJECTS)

# Target library names
STATIC_LIB = $(LIB_DIR)/$(TARGET_NAME).a
SHARED_LIB = $(LIB_DIR)/$(TARGET_NAME).so

# Default target
all: prepare $(SHARED_LIB)

# Create necessary directories
prepare:
	@mkdir -p $(BUILD_DIR)
	@mkdir -p $(LIB_DIR)

# Static library
$(STATIC_LIB): $(OBJECTS)
	@echo "Creating static library: $@"
	$(AR) rcs $@ $^

# Shared library
$(SHARED_LIB): $(OBJECTS)
	@echo "Creating shared library: $@"
	$(CXX) -shared -o $@ $^ $(LIBPATHS) $(LIBS)

# Compile C++ source files
$(BUILD_DIR)/%.o: $(SRC_DIR)/%.cpp
	@echo "Compiling C++: $<"
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Compile C source files
$(BUILD_DIR)/%.o: $(SRC_DIR)/%.c
	@echo "Compiling C: $<"
	$(CC) $(CFLAGS) -c $< -o $@

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)
	rm -rf $(LIB_DIR)

# Test target (builds a simple test program)
test: all
	@echo "Building test program..."
	@mkdir -p $(BUILD_DIR)
	$(CXX) $(CXXFLAGS) -o $(BUILD_DIR)/test_logger test/test_logger.cpp -L$(LIB_DIR) -larc_common_logger $(LIBPATHS) $(LIBS)
	@echo "Test program built: $(BUILD_DIR)/test_logger"

# Examples target (builds example programs)
examples: all
	@echo "Building example programs..."
	@mkdir -p $(BUILD_DIR)
	$(CC) $(CFLAGS) -o $(BUILD_DIR)/simple_example ../app/simple_example.c -L$(LIB_DIR) -larc_common_logger $(LIBPATHS) $(LIBS)
	$(CXX) $(CXXFLAGS) -o $(BUILD_DIR)/advanced_example ../app/advanced_example.cpp -L$(LIB_DIR) -larc_common_logger $(LIBPATHS) $(LIBS)
	@echo "Example programs built:"
	@echo "  - $(BUILD_DIR)/simple_example"
	@echo "  - $(BUILD_DIR)/advanced_example"

# Help target
help:
	@echo "Available targets:"
	@echo "  all       - Build both static and shared libraries (default)"
	@echo "  clean     - Remove all build artifacts"
	@echo "  test      - Build test program (requires test/test_logger.cpp)"
	@echo "  examples  - Build example programs"
	@echo "  help      - Show this help message"
	@echo ""

# Phony targets
.PHONY: all clean test examples help directories
