/**
 * @file test_logger.cpp
 * @brief Test program for ARC Common Logger SDK
 */

#include "arc_common_logger.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <cstdio>
#include <vector>

void test_basic_logging() {
    std::cout << "\n=== Testing Basic Logging ===" << std::endl;
    
    // Initialize with default configuration
    arc_log_result_t result = arc_logger_init_default("test_basic.log");
    if (result != ARC_LOG_SUCCESS) {
        std::cerr << "Failed to initialize logger: " << result << std::endl;
        return;
    }
    
    std::cout << "Logger initialized successfully" << std::endl;
    
    // Test different log levels
    arc_log_trace("This is a trace message: %d", 1);
    arc_log_debug("This is a debug message: %s", "debug");
    arc_log_info("This is an info message: %f", 3.14);
    arc_log_warn("This is a warning message");
    arc_log_error("This is an error message");
    arc_log_critical("This is a critical message");
    
    // Test generic log function
    arc_log(ARC_LOG_INFO, "Generic log message with number: %d", 42);
    
    arc_logger_flush();
    arc_logger_shutdown();
    
    std::cout << "Basic logging test completed" << std::endl;
}

void test_custom_configuration() {
    std::cout << "\n=== Testing Custom Configuration ===" << std::endl;
    
    arc_logger_config_t config;
    arc_logger_get_default_config(&config);
    
    // Customize configuration
    config.log_file_path = "test_custom.log";
    config.max_file_size = 1024 * 1024; // 1MB
    config.max_files = 3;
    config.level = ARC_LOG_DEBUG;
    config.pattern = "[%Y-%m-%d %H:%M:%S] [%^%l%$] %v";
    
    arc_log_result_t result = arc_logger_init(&config);
    if (result != ARC_LOG_SUCCESS) {
        std::cerr << "Failed to initialize logger with custom config: " << result << std::endl;
        return;
    }
    
    std::cout << "Logger initialized with custom configuration" << std::endl;
    
    // Test logging with custom pattern
    arc_log_info("Custom pattern test message");
    arc_log_debug("Debug message (should appear)");
    arc_log_trace("Trace message (should NOT appear due to level filter)");
    
    // Test level changes
    arc_logger_set_level(ARC_LOG_TRACE);
    arc_log_trace("Trace message (should appear now)");
    
    arc_logger_flush();
    arc_logger_shutdown();
    
    std::cout << "Custom configuration test completed" << std::endl;
}

void test_async_logging() {
    std::cout << "\n=== Testing Async Logging ===" << std::endl;
    
    arc_logger_config_t config;
    arc_logger_get_default_config(&config);
    
    // Enable async mode
    config.log_file_path = "test_async.log";
    config.async_mode = 1;
    config.async_queue_size = 1024;
    config.async_thread_count = 2;
    config.level = ARC_LOG_TRACE;
    
    arc_log_result_t result = arc_logger_init(&config);
    if (result != ARC_LOG_SUCCESS) {
        std::cerr << "Failed to initialize async logger: " << result << std::endl;
        return;
    }
    
    std::cout << "Async logger initialized successfully" << std::endl;
    
    // Generate many log messages quickly
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < 1000; ++i) {
        arc_log_info("Async log message #%d", i);
        if (i % 100 == 0) {
            arc_log_warn("Progress: %d/1000", i);
        }
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    std::cout << "Generated 1000 log messages in " << duration.count() << " ms" << std::endl;
    
    arc_logger_flush();
    arc_logger_shutdown();
    
    std::cout << "Async logging test completed" << std::endl;
}

void test_thread_safety() {
    std::cout << "\n=== Testing Thread Safety ===" << std::endl;
    
    arc_log_result_t result = arc_logger_init_default("test_threaded.log");
    if (result != ARC_LOG_SUCCESS) {
        std::cerr << "Failed to initialize logger for thread test: " << result << std::endl;
        return;
    }
    
    std::cout << "Testing concurrent logging from multiple threads..." << std::endl;
    
    const int num_threads = 4;
    const int messages_per_thread = 100;
    
    std::vector<std::thread> threads;
    
    // Launch multiple threads
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([t, messages_per_thread]() {
            for (int i = 0; i < messages_per_thread; ++i) {
                arc_log_info("Thread %d, message %d", t, i);
                if (i % 20 == 0) {
                    arc_log_warn("Thread %d progress: %d/%d", t, i, messages_per_thread);
                }
                // Small delay to interleave messages
                std::this_thread::sleep_for(std::chrono::microseconds(100));
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    arc_logger_flush();
    arc_logger_shutdown();
    
    std::cout << "Thread safety test completed" << std::endl;
}

void test_utility_functions() {
    std::cout << "\n=== Testing Utility Functions ===" << std::endl;
    
    // Test level string conversion
    for (int level = ARC_LOG_TRACE; level <= ARC_LOG_OFF; ++level) {
        arc_log_level_t lvl = static_cast<arc_log_level_t>(level);
        const char* str = arc_log_level_to_string(lvl);
        arc_log_level_t converted_back = arc_log_level_from_string(str);
        
        std::cout << "Level " << level << " -> \"" << str << "\" -> " << converted_back;
        if (lvl == converted_back) {
            std::cout << " ✓" << std::endl;
        } else {
            std::cout << " ✗" << std::endl;
        }
    }
    
    // Test invalid string conversion
    arc_log_level_t invalid = arc_log_level_from_string("INVALID");
    std::cout << "Invalid string conversion result: " << invalid << " (should be " << ARC_LOG_OFF << ")" << std::endl;
    
    std::cout << "Utility functions test completed" << std::endl;
}

int main() {
    std::cout << "ARC Common Logger SDK Test Program" << std::endl;
    std::cout << "===================================" << std::endl;
    
    try {
        test_basic_logging();
        test_custom_configuration();
        test_async_logging();
        test_thread_safety();
        test_utility_functions();
        
        std::cout << "\n=== All Tests Completed ===" << std::endl;
        std::cout << "Check the generated log files:" << std::endl;
        std::cout << "  - test_basic.log" << std::endl;
        std::cout << "  - test_custom.log" << std::endl;
        std::cout << "  - test_async.log" << std::endl;
        std::cout << "  - test_threaded.log" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
