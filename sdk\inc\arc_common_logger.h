#ifndef ARC_COMMON_LOGGER_H
#define ARC_COMMON_LOGGER_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file arc_common_logger.h
 * @brief ARC Common Logger SDK - A thread-safe logging library based on spdlog
 *
 * This SDK provides a simple C-style interface for logging with the following features:
 * - Rotating file sink support
 * - Optional async logging
 * - Pattern syntax support
 * - Thread-safe operations
 * - Compatible with C++11/14
 */

/* Log levels */
typedef enum {
    ARC_LOG_OFF = 0,
    ARC_LOG_CRITICAL = 1,
    ARC_LOG_ERROR = 2,
    ARC_LOG_WARN = 3,
    ARC_LOG_INFO = 4,
    ARC_LOG_DEBUG = 5,
    ARC_LOG_TRACE = 6
} arc_log_level_t;

/* Logger configuration structure */
typedef struct {
    const char* log_file_path;      /* Path to log file */
    unsigned long max_file_size;    /* Maximum file size in bytes before rotation */
    unsigned int max_files;         /* Maximum number of rotated files to keep */
    int async_mode;                 /* 0 = synchronous, 1 = asynchronous */
    const char* pattern;            /* Log pattern string (NULL for default) */
    arc_log_level_t level;          /* Minimum log level */
    unsigned int async_queue_size;  /* Async queue size (only used if async_mode = 1) */
    unsigned int async_thread_count; /* Number of async threads (only used if async_mode = 1) */
} arc_logger_config_t;

/* Return codes */
typedef enum {
    ARC_LOG_SUCCESS = 0,
    ARC_LOG_ERROR_INVALID_PARAM = -1,
    ARC_LOG_ERROR_INIT_FAILED = -2,
    ARC_LOG_ERROR_NOT_INITIALIZED = -3,
    ARC_LOG_ERROR_ALREADY_INITIALIZED = -4
} arc_log_result_t;

/**
 * @brief Initialize the logger with given configuration
 * @param config Logger configuration
 * @return ARC_LOG_SUCCESS on success, error code on failure
 */
arc_log_result_t arc_logger_init(const arc_logger_config_t* config);

/**
 * @brief Initialize the logger with default configuration
 * @param log_file_path Path to log file
 * @return ARC_LOG_SUCCESS on success, error code on failure
 */
arc_log_result_t arc_logger_init_default(const char* log_file_path);

/**
 * @brief Shutdown the logger and cleanup resources
 */
void arc_logger_shutdown(void);

/**
 * @brief Check if logger is initialized
 * @return 1 if initialized, 0 otherwise
 */
int arc_logger_is_initialized(void);

/**
 * @brief Set log level
 * @param level New log level
 * @return ARC_LOG_SUCCESS on success, error code on failure
 */
arc_log_result_t arc_logger_set_level(arc_log_level_t level);

/**
 * @brief Get current log level
 * @return Current log level
 */
arc_log_level_t arc_logger_get_level(void);

/**
 * @brief Set log pattern
 * @param pattern Pattern string (NULL to use default)
 * @return ARC_LOG_SUCCESS on success, error code on failure
 */
arc_log_result_t arc_logger_set_pattern(const char* pattern);

/**
 * @brief Force flush all pending log messages
 */
void arc_logger_flush(void);

/**
 * @brief Log a message with specified level
 * @param level Log level
 * @param format Format string (printf-style)
 * @param ... Variable arguments
 */
void arc_log(arc_log_level_t level, const char* format, ...);

/**
 * @brief Log a trace message
 * @param format Format string (printf-style)
 * @param ... Variable arguments
 */
void arc_log_trace(const char* format, ...);

/**
 * @brief Log a debug message
 * @param format Format string (printf-style)
 * @param ... Variable arguments
 */
void arc_log_debug(const char* format, ...);

/**
 * @brief Log an info message
 * @param format Format string (printf-style)
 * @param ... Variable arguments
 */
void arc_log_info(const char* format, ...);

/**
 * @brief Log a warning message
 * @param format Format string (printf-style)
 * @param ... Variable arguments
 */
void arc_log_warn(const char* format, ...);

/**
 * @brief Log an error message
 * @param format Format string (printf-style)
 * @param ... Variable arguments
 */
void arc_log_error(const char* format, ...);

/**
 * @brief Log a critical message
 * @param format Format string (printf-style)
 * @param ... Variable arguments
 */
void arc_log_critical(const char* format, ...);

/**
 * @brief Get default logger configuration
 * @param config Pointer to configuration structure to fill
 */
void arc_logger_get_default_config(arc_logger_config_t* config);

/**
 * @brief Convert log level enum to string
 * @param level Log level
 * @return String representation of log level
 */
const char* arc_log_level_to_string(arc_log_level_t level);

/**
 * @brief Convert string to log level enum
 * @param level_str String representation of log level
 * @return Log level enum (ARC_LOG_OFF if invalid)
 */
arc_log_level_t arc_log_level_from_string(const char* level_str);

#ifdef __cplusplus
}
#endif

#endif /* ARC_COMMON_LOGGER_H */