/**
 * @file performance_test.cpp
 * @brief Performance testing program for ARC Common Logger SDK
 * 
 * This program tests the performance characteristics of the logger under various conditions:
 * - Single-threaded vs multi-threaded logging
 * - Synchronous vs asynchronous logging
 * - Different message sizes and frequencies
 * - Memory usage and throughput measurements
 */

#include "../sdk/inc/arc_common_logger.h"
#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <random>
#include <sstream>
#include <iomanip>
#include <cstring>
#include <sys/resource.h>
#include <unistd.h>

class PerformanceTest {
private:
    std::mt19937 rng;
    std::uniform_int_distribution<int> dist;
    std::atomic<long> total_messages{0};
    std::atomic<long> total_bytes{0};
    
    // Performance metrics
    struct TestResult {
        std::string test_name;
        long messages_count;
        long total_bytes;
        double duration_ms;
        double messages_per_second;
        double mb_per_second;
        long peak_memory_kb;
    };
    
    std::vector<TestResult> results;
    
public:
    PerformanceTest() : rng(std::random_device{}()), dist(1, 1000) {}
    
    // Get current memory usage in KB
    long getCurrentMemoryUsage() {
        struct rusage usage;
        getrusage(RUSAGE_SELF, &usage);
        return usage.ru_maxrss; // Peak memory usage in KB
    }
    
    // Generate test message of specified size
    std::string generateMessage(size_t target_size) {
        std::ostringstream oss;
        oss << "Test message " << dist(rng) << " - ";
        
        // Fill to target size
        while (oss.str().length() < target_size - 10) {
            oss << "data" << dist(rng) << " ";
        }
        
        return oss.str();
    }
    
    // Single-threaded synchronous performance test
    void testSyncSingleThread(int message_count, size_t message_size) {
        std::cout << "\n=== Sync Single Thread Test ===" << std::endl;
        std::cout << "Messages: " << message_count << ", Size: " << message_size << " bytes" << std::endl;
        
        arc_logger_config_t config;
        arc_logger_get_default_config(&config);
        config.log_file_path = "perf_sync_single.log";
        config.max_file_size = 100 * 1024 * 1024; // 100MB
        config.max_files = 3;
        config.async_mode = 0; // Synchronous
        config.level = ARC_LOG_INFO;
        config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%l] %v";
        
        if (arc_logger_init(&config) != ARC_LOG_SUCCESS) {
            std::cerr << "Failed to initialize logger" << std::endl;
            return;
        }
        
        long memory_start = getCurrentMemoryUsage();
        auto start = std::chrono::high_resolution_clock::now();
        
        long bytes_written = 0;
        for (int i = 0; i < message_count; ++i) {
            std::string msg = generateMessage(message_size);
            arc_log_info("%s", msg.c_str());
            bytes_written += msg.length();
            
            if (i % 10000 == 0 && i > 0) {
                std::cout << "Progress: " << i << "/" << message_count << std::endl;
            }
        }
        
        arc_logger_flush();
        auto end = std::chrono::high_resolution_clock::now();
        long memory_peak = getCurrentMemoryUsage();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        TestResult result;
        result.test_name = "Sync Single Thread";
        result.messages_count = message_count;
        result.total_bytes = bytes_written;
        result.duration_ms = duration.count();
        result.messages_per_second = (double)message_count / (duration.count() / 1000.0);
        result.mb_per_second = (double)bytes_written / (1024.0 * 1024.0) / (duration.count() / 1000.0);
        result.peak_memory_kb = memory_peak - memory_start;
        
        results.push_back(result);
        
        std::cout << "Completed in " << duration.count() << " ms" << std::endl;
        std::cout << "Throughput: " << std::fixed << std::setprecision(2) 
                  << result.messages_per_second << " msg/s, " 
                  << result.mb_per_second << " MB/s" << std::endl;
        
        arc_logger_shutdown();
    }
    
    // Single-threaded asynchronous performance test
    void testAsyncSingleThread(int message_count, size_t message_size) {
        std::cout << "\n=== Async Single Thread Test ===" << std::endl;
        std::cout << "Messages: " << message_count << ", Size: " << message_size << " bytes" << std::endl;
        
        arc_logger_config_t config;
        arc_logger_get_default_config(&config);
        config.log_file_path = "perf_async_single.log";
        config.max_file_size = 100 * 1024 * 1024; // 100MB
        config.max_files = 3;
        config.async_mode = 1; // Asynchronous
        config.async_queue_size = 32768;
        config.async_thread_count = 2;
        config.level = ARC_LOG_INFO;
        config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%l] %v";
        
        if (arc_logger_init(&config) != ARC_LOG_SUCCESS) {
            std::cerr << "Failed to initialize async logger" << std::endl;
            return;
        }
        
        long memory_start = getCurrentMemoryUsage();
        auto start = std::chrono::high_resolution_clock::now();
        
        long bytes_written = 0;
        for (int i = 0; i < message_count; ++i) {
            std::string msg = generateMessage(message_size);
            arc_log_info("%s", msg.c_str());
            bytes_written += msg.length();
            
            if (i % 10000 == 0 && i > 0) {
                std::cout << "Progress: " << i << "/" << message_count << std::endl;
            }
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        
        // Wait for async processing to complete
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        arc_logger_flush();
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        long memory_peak = getCurrentMemoryUsage();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        TestResult result;
        result.test_name = "Async Single Thread";
        result.messages_count = message_count;
        result.total_bytes = bytes_written;
        result.duration_ms = duration.count();
        result.messages_per_second = (double)message_count / (duration.count() / 1000.0);
        result.mb_per_second = (double)bytes_written / (1024.0 * 1024.0) / (duration.count() / 1000.0);
        result.peak_memory_kb = memory_peak - memory_start;
        
        results.push_back(result);
        
        std::cout << "Completed in " << duration.count() << " ms" << std::endl;
        std::cout << "Throughput: " << std::fixed << std::setprecision(2) 
                  << result.messages_per_second << " msg/s, " 
                  << result.mb_per_second << " MB/s" << std::endl;
        
        arc_logger_shutdown();
    }
    
    // Multi-threaded performance test
    void testMultiThreaded(int num_threads, int messages_per_thread, size_t message_size, bool async_mode) {
        std::cout << "\n=== Multi-threaded Test (" << (async_mode ? "Async" : "Sync") << ") ===" << std::endl;
        std::cout << "Threads: " << num_threads << ", Messages per thread: " << messages_per_thread 
                  << ", Size: " << message_size << " bytes" << std::endl;
        
        arc_logger_config_t config;
        arc_logger_get_default_config(&config);
        config.log_file_path = async_mode ? "perf_async_multi.log" : "perf_sync_multi.log";
        config.max_file_size = 200 * 1024 * 1024; // 200MB
        config.max_files = 5;
        config.async_mode = async_mode ? 1 : 0;
        config.async_queue_size = 65536;
        config.async_thread_count = 4;
        config.level = ARC_LOG_INFO;
        config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v";
        
        if (arc_logger_init(&config) != ARC_LOG_SUCCESS) {
            std::cerr << "Failed to initialize multi-threaded logger" << std::endl;
            return;
        }
        
        total_messages = 0;
        total_bytes = 0;
        
        long memory_start = getCurrentMemoryUsage();
        auto start = std::chrono::high_resolution_clock::now();
        
        std::vector<std::thread> threads;
        
        for (int t = 0; t < num_threads; ++t) {
            threads.emplace_back([this, t, messages_per_thread, message_size]() {
                long local_bytes = 0;
                for (int i = 0; i < messages_per_thread; ++i) {
                    std::string msg = generateMessage(message_size);
                    arc_log_info("Thread %d: %s", t, msg.c_str());
                    local_bytes += msg.length();
                    
                    if (i % 5000 == 0 && i > 0) {
                        std::cout << "Thread " << t << " progress: " << i << "/" << messages_per_thread << std::endl;
                    }
                }
                total_messages += messages_per_thread;
                total_bytes += local_bytes;
            });
        }
        
        // Wait for all threads to complete
        for (auto& thread : threads) {
            thread.join();
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        
        if (async_mode) {
            // Wait for async processing
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
        
        arc_logger_flush();
        
        if (async_mode) {
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
        
        long memory_peak = getCurrentMemoryUsage();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        TestResult result;
        result.test_name = std::string("Multi-threaded ") + (async_mode ? "Async" : "Sync");
        result.messages_count = total_messages;
        result.total_bytes = total_bytes;
        result.duration_ms = duration.count();
        result.messages_per_second = (double)total_messages / (duration.count() / 1000.0);
        result.mb_per_second = (double)total_bytes / (1024.0 * 1024.0) / (duration.count() / 1000.0);
        result.peak_memory_kb = memory_peak - memory_start;
        
        results.push_back(result);
        
        std::cout << "Completed in " << duration.count() << " ms" << std::endl;
        std::cout << "Total messages: " << total_messages << std::endl;
        std::cout << "Throughput: " << std::fixed << std::setprecision(2) 
                  << result.messages_per_second << " msg/s, " 
                  << result.mb_per_second << " MB/s" << std::endl;
        
        arc_logger_shutdown();
    }
    
    // Print summary of all test results
    void printSummary() {
        std::cout << "\n" << std::string(80, '=') << std::endl;
        std::cout << "PERFORMANCE TEST SUMMARY" << std::endl;
        std::cout << std::string(80, '=') << std::endl;
        
        std::cout << std::left << std::setw(25) << "Test Name"
                  << std::setw(12) << "Messages"
                  << std::setw(10) << "Duration"
                  << std::setw(12) << "Msg/sec"
                  << std::setw(12) << "MB/sec"
                  << std::setw(12) << "Memory(KB)" << std::endl;
        std::cout << std::string(80, '-') << std::endl;
        
        for (const auto& result : results) {
            std::cout << std::left << std::setw(25) << result.test_name
                      << std::setw(12) << result.messages_count
                      << std::setw(10) << std::fixed << std::setprecision(0) << result.duration_ms << "ms"
                      << std::setw(12) << std::fixed << std::setprecision(0) << result.messages_per_second
                      << std::setw(12) << std::fixed << std::setprecision(2) << result.mb_per_second
                      << std::setw(12) << result.peak_memory_kb << std::endl;
        }
        
        std::cout << std::string(80, '=') << std::endl;
    }
};

int main() {
    std::cout << "ARC Common Logger SDK - Performance Test" << std::endl;
    std::cout << "=========================================" << std::endl;
    
    PerformanceTest test;
    
    try {
        // Test different scenarios
        test.testSyncSingleThread(50000, 100);    // 50K messages, 100 bytes each
        test.testAsyncSingleThread(50000, 100);   // 50K messages, 100 bytes each
        
        test.testSyncSingleThread(10000, 1000);   // 10K messages, 1KB each
        test.testAsyncSingleThread(10000, 1000);  // 10K messages, 1KB each
        
        test.testMultiThreaded(4, 10000, 100, false);  // 4 threads, sync
        test.testMultiThreaded(4, 10000, 100, true);   // 4 threads, async
        
        test.testMultiThreaded(8, 5000, 200, false);   // 8 threads, sync
        test.testMultiThreaded(8, 5000, 200, true);    // 8 threads, async
        
        test.printSummary();
        
        std::cout << "\nPerformance test completed successfully!" << std::endl;
        std::cout << "Check the generated log files for output verification." << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Performance test failed: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
