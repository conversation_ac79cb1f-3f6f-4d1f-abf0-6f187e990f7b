/**
 * @file simple_example.c
 * @brief Simple example demonstrating ARC Common Logger SDK usage in C
 */

#include "arc_common_logger.h"
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

int main() {
    printf("ARC Common Logger SDK - Simple Example\n");
    printf("=====================================\n\n");
    
    // Initialize logger with default configuration
    printf("Initializing logger...\n");
    arc_log_result_t result = arc_logger_init_default("simple_example.log");
    if (result != ARC_LOG_SUCCESS) {
        printf("Failed to initialize logger: %d\n", result);
        return 1;
    }
    printf("Logger initialized successfully!\n\n");
    
    // Log some messages
    printf("Logging messages...\n");
    arc_log_info("Application started");
    arc_log_info("This is an info message with number: %d", 42);
    arc_log_warn("This is a warning message");
    arc_log_error("This is an error message with string: %s", "example error");
    
    // Change log level
    printf("Changing log level to DEBUG...\n");
    arc_logger_set_level(ARC_LOG_DEBUG);
    
    arc_log_debug("This debug message should appear");
    arc_log_trace("This trace message should NOT appear (level is DEBUG)");
    
    // Change log level to TRACE
    printf("Changing log level to TRACE...\n");
    arc_logger_set_level(ARC_LOG_TRACE);
    
    arc_log_trace("This trace message should appear now");
    
    // Demonstrate different log levels
    printf("Demonstrating all log levels...\n");
    arc_log_trace("TRACE: Very detailed information");
    arc_log_debug("DEBUG: Debug information");
    arc_log_info("INFO: General information");
    arc_log_warn("WARN: Warning message");
    arc_log_error("ERROR: Error occurred");
    arc_log_critical("CRITICAL: Critical error!");
    
    // Force flush to ensure all messages are written
    printf("Flushing log messages...\n");
    arc_logger_flush();
    
    // Cleanup
    printf("Shutting down logger...\n");
    arc_logger_shutdown();
    
    printf("\nExample completed successfully!\n");
    printf("Check 'simple_example.log' for the logged messages.\n");
    
    return 0;
}
