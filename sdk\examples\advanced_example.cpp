/**
 * @file advanced_example.cpp
 * @brief Advanced example demonstrating ARC Common Logger SDK features
 */

#include "arc_common_logger.h"
#include <iostream>
#include <thread>
#include <vector>
#include <chrono>
#include <random>

class LoggerExample {
private:
    std::mt19937 rng;
    std::uniform_int_distribution<int> dist;
    
public:
    LoggerExample() : rng(std::random_device{}()), dist(1, 1000) {}
    
    void demonstrate_sync_logging() {
        std::cout << "\n=== Synchronous Logging Demo ===" << std::endl;
        
        arc_logger_config_t config;
        arc_logger_get_default_config(&config);
        
        config.log_file_path = "sync_demo.log";
        config.max_file_size = 1024 * 1024; // 1MB
        config.max_files = 3;
        config.async_mode = 0; // Synchronous
        config.level = ARC_LOG_TRACE;
        config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%^%5l%$] [thread %t] %v";
        
        if (arc_logger_init(&config) != ARC_LOG_SUCCESS) {
            std::cerr << "Failed to initialize sync logger" << std::endl;
            return;
        }
        
        std::cout << "Logging 1000 messages synchronously..." << std::endl;
        auto start = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < 1000; ++i) {
            int random_val = dist(rng);
            
            if (i % 100 == 0) {
                arc_log_info("Progress: %d/1000 (random: %d)", i, random_val);
            } else if (random_val > 900) {
                arc_log_error("High random value detected: %d", random_val);
            } else if (random_val < 100) {
                arc_log_warn("Low random value detected: %d", random_val);
            } else {
                arc_log_debug("Normal operation: iteration %d, value %d", i, random_val);
            }
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        arc_log_info("Synchronous logging completed in %ld ms", duration.count());
        std::cout << "Sync logging took: " << duration.count() << " ms" << std::endl;
        
        arc_logger_shutdown();
    }
    
    void demonstrate_async_logging() {
        std::cout << "\n=== Asynchronous Logging Demo ===" << std::endl;
        
        arc_logger_config_t config;
        arc_logger_get_default_config(&config);
        
        config.log_file_path = "async_demo.log";
        config.max_file_size = 2 * 1024 * 1024; // 2MB
        config.max_files = 5;
        config.async_mode = 1; // Asynchronous
        config.async_queue_size = 8192;
        config.async_thread_count = 2;
        config.level = ARC_LOG_TRACE;
        config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%^%5l%$] [thread %t] %v";
        
        if (arc_logger_init(&config) != ARC_LOG_SUCCESS) {
            std::cerr << "Failed to initialize async logger" << std::endl;
            return;
        }
        
        std::cout << "Logging 5000 messages asynchronously..." << std::endl;
        auto start = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < 5000; ++i) {
            int random_val = dist(rng);
            
            if (i % 500 == 0) {
                arc_log_info("Async progress: %d/5000 (random: %d)", i, random_val);
            } else if (random_val > 950) {
                arc_log_critical("Critical random value: %d", random_val);
            } else if (random_val > 900) {
                arc_log_error("High random value: %d", random_val);
            } else if (random_val < 50) {
                arc_log_warn("Very low random value: %d", random_val);
            } else if (random_val < 100) {
                arc_log_debug("Low random value: %d", random_val);
            } else {
                arc_log_trace("Trace: iteration %d, value %d", i, random_val);
            }
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        arc_log_info("Asynchronous logging completed in %ld ms", duration.count());
        std::cout << "Async logging took: " << duration.count() << " ms" << std::endl;
        
        // Wait a bit for async processing to complete
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        arc_logger_shutdown();
    }
    
    void demonstrate_multithreaded_logging() {
        std::cout << "\n=== Multithreaded Logging Demo ===" << std::endl;
        
        arc_logger_config_t config;
        arc_logger_get_default_config(&config);
        
        config.log_file_path = "multithread_demo.log";
        config.max_file_size = 5 * 1024 * 1024; // 5MB
        config.max_files = 3;
        config.async_mode = 1; // Use async for better performance
        config.async_queue_size = 16384;
        config.async_thread_count = 3;
        config.level = ARC_LOG_DEBUG;
        config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%^%5l%$] [thread %t] %v";
        
        if (arc_logger_init(&config) != ARC_LOG_SUCCESS) {
            std::cerr << "Failed to initialize multithreaded logger" << std::endl;
            return;
        }
        
        const int num_threads = 8;
        const int messages_per_thread = 500;
        
        std::cout << "Starting " << num_threads << " threads, each logging " 
                  << messages_per_thread << " messages..." << std::endl;
        
        auto start = std::chrono::high_resolution_clock::now();
        
        std::vector<std::thread> threads;
        
        for (int t = 0; t < num_threads; ++t) {
            threads.emplace_back([this, t, messages_per_thread]() {
                for (int i = 0; i < messages_per_thread; ++i) {
                    int random_val = dist(rng);
                    
                    if (i % 100 == 0) {
                        arc_log_info("Thread %d progress: %d/%d", t, i, messages_per_thread);
                    } else if (random_val > 900) {
                        arc_log_error("Thread %d: High value %d at iteration %d", t, random_val, i);
                    } else if (random_val < 100) {
                        arc_log_warn("Thread %d: Low value %d at iteration %d", t, random_val, i);
                    } else {
                        arc_log_debug("Thread %d: Normal operation %d (value: %d)", t, i, random_val);
                    }
                    
                    // Small delay to create more realistic logging pattern
                    if (i % 50 == 0) {
                        std::this_thread::sleep_for(std::chrono::microseconds(100));
                    }
                }
                
                arc_log_info("Thread %d completed", t);
            });
        }
        
        // Wait for all threads to complete
        for (auto& thread : threads) {
            thread.join();
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        arc_log_info("All threads completed in %ld ms", duration.count());
        std::cout << "Multithreaded logging completed in: " << duration.count() << " ms" << std::endl;
        
        // Wait for async processing
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        arc_logger_shutdown();
    }
    
    void demonstrate_pattern_changes() {
        std::cout << "\n=== Pattern Change Demo ===" << std::endl;
        
        if (arc_logger_init_default("pattern_demo.log") != ARC_LOG_SUCCESS) {
            std::cerr << "Failed to initialize pattern demo logger" << std::endl;
            return;
        }
        
        arc_log_info("Default pattern message");
        
        // Change to simple pattern
        arc_logger_set_pattern("%H:%M:%S [%l] %v");
        arc_log_info("Simple pattern message");
        
        // Change to detailed pattern
        arc_logger_set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%8l%$] [%t] [%s:%#] %v");
        arc_log_info("Detailed pattern message");
        
        // Change to minimal pattern
        arc_logger_set_pattern("%v");
        arc_log_info("Minimal pattern - just the message");
        
        arc_logger_shutdown();
    }
};

int main() {
    std::cout << "ARC Common Logger SDK - Advanced Example" << std::endl;
    std::cout << "=========================================" << std::endl;
    
    try {
        LoggerExample example;
        
        example.demonstrate_sync_logging();
        example.demonstrate_async_logging();
        example.demonstrate_multithreaded_logging();
        example.demonstrate_pattern_changes();
        
        std::cout << "\n=== All Demonstrations Completed ===" << std::endl;
        std::cout << "Generated log files:" << std::endl;
        std::cout << "  - sync_demo.log" << std::endl;
        std::cout << "  - async_demo.log" << std::endl;
        std::cout << "  - multithread_demo.log" << std::endl;
        std::cout << "  - pattern_demo.log" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Example failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
