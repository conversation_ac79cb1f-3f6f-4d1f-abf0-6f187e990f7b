////////////////////////////////////////////////////////////////////////
//              ArcSoft Common Logger SDK
////////////////////////////////////////// /////////////////////////////

Introduction:
Based on spdlog, ArcSoft Common Logger SDK provides a simple C-style interface for logging with the following features:
- Rotating file sink support
- Optional async logging
- Pattern syntax support
- Thread-safe operations
- Compatible with C++11/14

Publish date:
06/03/2025

Version:
1.0.40023.1

Supported platforms:
LINUX_ARM64

Compile options:
Default

Library dependency:
sdplog (1.15.3)

File List:
├── app
│   ├── advanced_example.cpp
│   └── simple_example.c
├── autobuild.sh
├── Related_Project
│   └── spdlog
├── releasenotes.txt
└── sdk
    ├── inc
    │   └── arc_common_logger.h
    ├── Makefile
    ├── src
    │   └── arc_common_logger.cpp
    ├── test
    │   └── test_logger.cpp
    └── version.h

Change logs:


Known issues:
None

Notes:

