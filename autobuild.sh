#!/bin/bash

set -e
COMPILE_MODEL=debug
printUsage() {
	echo "Usage:"
	echo ""
	echo "    $0 <current-autobuild-number> <current-compile-model:0 release, 1 debug> <platform: 0:linux aarch64-none-linux-gnu-"
	echo ""
}

if [ $# -ne 3 ];then
	printUsage
	exit 1
fi

if [ ! -n "$(echo $1 | sed -n "/^[0-9]\+$/p")" ];then 
	printUsage
	exit 1
fi
if [ $2 == 0 ];then
	COMPILE_MODEL=release
fi
if [ $2 == 1 ];then
	COMPILE_MODEL=debug
fi

if [ ! -n "$(echo $3 | sed -n "/^[0-9]\+$/p")" ];then 
	printUsage
	exit 1
fi

sed -i "27c #define VERSION_BUILD \"$1\"" sdk/version.h

#TDA4 LINUX
if [ $3 -eq 0 ];then
	echo "build linux aarch64-none-linux-gnu- "

    # unzip spdlog
    if [ ! -d "Related_Project/spdlog" ]; then
        echo "spdlog not exist..."
        mkdir -p Related_Project/spdlog
        unzip Related_Project/spdlog_arm_linux_1.5.3.zip -d Related_Project/spdlog
    fi

	if [ ${COMPILE_MODEL} == "debug" ];then
		make -C sdk clean
		make -C sdk all test examples COMPILE_MODEL=debug 
	else
        make -C sdk clean
		make -C sdk all test examples COMPILE_MODEL=release 
	fi
fi

rm -rf publish
mkdir -p publish publish/inc  publish/lib  publish/samplecode
cp -rfa releasenotes.txt publish/
cp -rfa sdk/inc/* publish/inc/
cp -rfa sdk/test/* publish/samplecode/
cp -rfa app/* publish/samplecode/
cp -rfa sdk/lib/* publish/lib/
cp -a Related_Project/spdlog/lib/libspdlog.* publish/lib/
cp -rfa build/* publish/samplecode && rm publish/samplecode/*.o

VERSION_CODEBASE=$(cat sdk/version.h | grep VERSION_CODEBASE | awk '{print $3}' | awk -F'"' '{print $2}')
VERSION_MAJOR=$(cat sdk/version.h | grep VERSION_MAJOR | awk '{print $3}' | awk -F'"' '{print $2}')
VERSION_MINOR=$(cat sdk/version.h | grep VERSION_MINOR | awk '{print $3}' | awk -F'"' '{print $2}')
VERSION_BUILD=$(cat sdk/version.h | grep VERSION_BUILD | awk '{print $3}' | awk -F'"' '{print $2}')
echo "current sdk version:${VERSION_CODEBASE}.${VERSION_MAJOR}.${VERSION_MINOR}.${VERSION_BUILD}"

BUILD_TIME=`date '+%Y%m%d_%H%M%S'`
cd publish && if [ -f *.zip ]; then rm *.zip; fi && zip -r -y -p arc_common_logger_${BUILD_TIME}.zip ./*

