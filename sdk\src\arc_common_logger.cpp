/**
 * @file arc_common_logger.cpp
 * @brief Implementation of ARC Common Logger SDK
 */

#include "arc_common_logger.h"

// spdlog includes
#include <spdlog/spdlog.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/async.h>
#include <spdlog/pattern_formatter.h>

// Standard includes
#include <memory>
#include <mutex>
#include <cstdarg>
#include <cstring>
#include <string>
#include <cctype>

namespace {
    // Internal state
    std::shared_ptr<spdlog::logger> g_logger;
    std::mutex g_logger_mutex;
    bool g_initialized = false;
    arc_logger_config_t g_current_config;

    // Default configuration values
    const char* DEFAULT_PATTERN = "[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v";
    const unsigned long DEFAULT_MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    const unsigned int DEFAULT_MAX_FILES = 5;
    const unsigned int DEFAULT_ASYNC_QUEUE_SIZE = 8192;
    const unsigned int DEFAULT_ASYNC_THREAD_COUNT = 1;

    // Convert ARC log level to spdlog level
    spdlog::level::level_enum arc_to_spdlog_level(arc_log_level_t level) {
        switch (level) {
            case ARC_LOG_TRACE:    return spdlog::level::trace;
            case ARC_LOG_DEBUG:    return spdlog::level::debug;
            case ARC_LOG_INFO:     return spdlog::level::info;
            case ARC_LOG_WARN:     return spdlog::level::warn;
            case ARC_LOG_ERROR:    return spdlog::level::err;
            case ARC_LOG_CRITICAL: return spdlog::level::critical;
            case ARC_LOG_OFF:      return spdlog::level::off;
            default:               return spdlog::level::info;
        }
    }

    // Convert spdlog level to ARC log level
    arc_log_level_t spdlog_to_arc_level(spdlog::level::level_enum level) {
        switch (level) {
            case spdlog::level::trace:    return ARC_LOG_TRACE;
            case spdlog::level::debug:    return ARC_LOG_DEBUG;
            case spdlog::level::info:     return ARC_LOG_INFO;
            case spdlog::level::warn:     return ARC_LOG_WARN;
            case spdlog::level::err:      return ARC_LOG_ERROR;
            case spdlog::level::critical: return ARC_LOG_CRITICAL;
            case spdlog::level::off:      return ARC_LOG_OFF;
            default:                      return ARC_LOG_INFO;
        }
    }

    // Validate configuration
    bool validate_config(const arc_logger_config_t* config) {
        if (!config) return false;
        if (!config->log_file_path || strlen(config->log_file_path) == 0) return false;
        if (config->max_file_size == 0) return false;
        if (config->max_files == 0) return false;
        if (config->level < ARC_LOG_TRACE || config->level > ARC_LOG_OFF) return false;
        if (config->async_mode != 0 && config->async_mode != 1) return false;
        if (config->async_mode == 1) {
            if (config->async_queue_size == 0) return false;
            if (config->async_thread_count == 0) return false;
        }
        return true;
    }

    // Create logger based on configuration
    std::shared_ptr<spdlog::logger> create_logger(const arc_logger_config_t* config) {
        try {
            std::shared_ptr<spdlog::logger> logger;

            if (config->async_mode) {
                // Initialize async thread pool if not already done
                if (!spdlog::thread_pool()) {
                    spdlog::init_thread_pool(config->async_queue_size, config->async_thread_count);
                }

                // Create async rotating file logger
                logger = spdlog::create_async<spdlog::sinks::rotating_file_sink_mt>(
                    "arc_logger",
                    config->log_file_path,
                    config->max_file_size,
                    config->max_files
                );
            } else {
                // Create synchronous rotating file logger
                logger = spdlog::rotating_logger_mt(
                    "arc_logger",
                    config->log_file_path,
                    config->max_file_size,
                    config->max_files
                );
            }

            if (logger) {
                // Set log level
                logger->set_level(arc_to_spdlog_level(config->level));

                // Set pattern
                const char* pattern = config->pattern ? config->pattern : DEFAULT_PATTERN;
                logger->set_pattern(pattern);

                // Flush on warning and above
                logger->flush_on(spdlog::level::warn);
            }

            return logger;
        } catch (const std::exception&) {
            return nullptr;
        }
    }

    // Thread-safe logging function
    void log_message(arc_log_level_t level, const char* format, va_list args) {
        std::lock_guard<std::mutex> lock(g_logger_mutex);

        if (!g_initialized || !g_logger) {
            return;
        }

        // Format message
        char buffer[4096];
        vsnprintf(buffer, sizeof(buffer), format, args);
        buffer[sizeof(buffer) - 1] = '\0';

        // Log message
        spdlog::level::level_enum spdlog_level = arc_to_spdlog_level(level);
        g_logger->log(spdlog_level, buffer);
    }
}

// C API Implementation
extern "C" {

arc_log_result_t arc_logger_init(const arc_logger_config_t* config) {
    std::lock_guard<std::mutex> lock(g_logger_mutex);

    if (g_initialized) {
        return ARC_LOG_ERROR_ALREADY_INITIALIZED;
    }

    if (!validate_config(config)) {
        return ARC_LOG_ERROR_INVALID_PARAM;
    }

    // Drop any existing logger
    if (g_logger) {
        spdlog::drop("arc_logger");
        g_logger.reset();
    }

    // Create new logger
    g_logger = create_logger(config);
    if (!g_logger) {
        return ARC_LOG_ERROR_INIT_FAILED;
    }

    // Store configuration
    g_current_config = *config;

    // Allocate and copy strings to avoid dangling pointers
    if (config->log_file_path) {
        size_t len = strlen(config->log_file_path) + 1;
        char* path_copy = new char[len];
        strcpy(path_copy, config->log_file_path);
        g_current_config.log_file_path = path_copy;
    }

    if (config->pattern) {
        size_t len = strlen(config->pattern) + 1;
        char* pattern_copy = new char[len];
        strcpy(pattern_copy, config->pattern);
        g_current_config.pattern = pattern_copy;
    }

    g_initialized = true;
    return ARC_LOG_SUCCESS;
}

arc_log_result_t arc_logger_init_default(const char* log_file_path) {
    if (!log_file_path || strlen(log_file_path) == 0) {
        return ARC_LOG_ERROR_INVALID_PARAM;
    }

    arc_logger_config_t config;
    arc_logger_get_default_config(&config);
    config.log_file_path = log_file_path;

    return arc_logger_init(&config);
}

void arc_logger_shutdown(void) {
    std::lock_guard<std::mutex> lock(g_logger_mutex);

    if (!g_initialized) {
        return;
    }

    // Flush and drop logger
    if (g_logger) {
        g_logger->flush();
        spdlog::drop("arc_logger");
        g_logger.reset();
    }

    // Cleanup allocated strings
    if (g_current_config.log_file_path) {
        delete[] g_current_config.log_file_path;
        g_current_config.log_file_path = nullptr;
    }

    if (g_current_config.pattern) {
        delete[] g_current_config.pattern;
        g_current_config.pattern = nullptr;
    }

    // Shutdown spdlog
    spdlog::shutdown();

    g_initialized = false;
}

int arc_logger_is_initialized(void) {
    std::lock_guard<std::mutex> lock(g_logger_mutex);
    return g_initialized ? 1 : 0;
}

arc_log_result_t arc_logger_set_level(arc_log_level_t level) {
    std::lock_guard<std::mutex> lock(g_logger_mutex);

    if (!g_initialized || !g_logger) {
        return ARC_LOG_ERROR_NOT_INITIALIZED;
    }

    if (level < ARC_LOG_TRACE || level > ARC_LOG_OFF) {
        return ARC_LOG_ERROR_INVALID_PARAM;
    }

    g_logger->set_level(arc_to_spdlog_level(level));
    g_current_config.level = level;

    return ARC_LOG_SUCCESS;
}

arc_log_level_t arc_logger_get_level(void) {
    std::lock_guard<std::mutex> lock(g_logger_mutex);

    if (!g_initialized || !g_logger) {
        return ARC_LOG_OFF;
    }

    return spdlog_to_arc_level(g_logger->level());
}

arc_log_result_t arc_logger_set_pattern(const char* pattern) {
    std::lock_guard<std::mutex> lock(g_logger_mutex);

    if (!g_initialized || !g_logger) {
        return ARC_LOG_ERROR_NOT_INITIALIZED;
    }

    try {
        const char* new_pattern = pattern ? pattern : DEFAULT_PATTERN;
        g_logger->set_pattern(new_pattern);

        // Update stored pattern
        if (g_current_config.pattern) {
            delete[] g_current_config.pattern;
            g_current_config.pattern = nullptr;
        }

        if (pattern) {
            size_t len = strlen(pattern) + 1;
            char* pattern_copy = new char[len];
            strcpy(pattern_copy, pattern);
            g_current_config.pattern = pattern_copy;
        }

        return ARC_LOG_SUCCESS;
    } catch (const std::exception&) {
        return ARC_LOG_ERROR_INVALID_PARAM;
    }
}

void arc_logger_flush(void) {
    std::lock_guard<std::mutex> lock(g_logger_mutex);

    if (g_initialized && g_logger) {
        g_logger->flush();
    }
}

// Logging functions
void arc_log(arc_log_level_t level, const char* format, ...) {
    if (!format) return;

    va_list args;
    va_start(args, format);
    log_message(level, format, args);
    va_end(args);
}

void arc_log_trace(const char* format, ...) {
    if (!format) return;

    va_list args;
    va_start(args, format);
    log_message(ARC_LOG_TRACE, format, args);
    va_end(args);
}

void arc_log_debug(const char* format, ...) {
    if (!format) return;

    va_list args;
    va_start(args, format);
    log_message(ARC_LOG_DEBUG, format, args);
    va_end(args);
}

void arc_log_info(const char* format, ...) {
    if (!format) return;

    va_list args;
    va_start(args, format);
    log_message(ARC_LOG_INFO, format, args);
    va_end(args);
}

void arc_log_warn(const char* format, ...) {
    if (!format) return;

    va_list args;
    va_start(args, format);
    log_message(ARC_LOG_WARN, format, args);
    va_end(args);
}

void arc_log_error(const char* format, ...) {
    if (!format) return;

    va_list args;
    va_start(args, format);
    log_message(ARC_LOG_ERROR, format, args);
    va_end(args);
}

void arc_log_critical(const char* format, ...) {
    if (!format) return;

    va_list args;
    va_start(args, format);
    log_message(ARC_LOG_CRITICAL, format, args);
    va_end(args);
}

// Utility functions
void arc_logger_get_default_config(arc_logger_config_t* config) {
    if (!config) return;

    config->log_file_path = nullptr;  // Must be set by caller
    config->max_file_size = DEFAULT_MAX_FILE_SIZE;
    config->max_files = DEFAULT_MAX_FILES;
    config->async_mode = 0;  // Synchronous by default
    config->pattern = nullptr;  // Use default pattern
    config->level = ARC_LOG_INFO;
    config->async_queue_size = DEFAULT_ASYNC_QUEUE_SIZE;
    config->async_thread_count = DEFAULT_ASYNC_THREAD_COUNT;
}

const char* arc_log_level_to_string(arc_log_level_t level) {
    switch (level) {
        case ARC_LOG_TRACE:    return "TRACE";
        case ARC_LOG_DEBUG:    return "DEBUG";
        case ARC_LOG_INFO:     return "INFO";
        case ARC_LOG_WARN:     return "WARN";
        case ARC_LOG_ERROR:    return "ERROR";
        case ARC_LOG_CRITICAL: return "CRITICAL";
        case ARC_LOG_OFF:      return "OFF";
        default:               return "UNKNOWN";
    }
}

arc_log_level_t arc_log_level_from_string(const char* level_str) {
    if (!level_str) return ARC_LOG_OFF;

    // Convert to uppercase for comparison
    std::string str(level_str);
    for (char& c : str) {
        c = std::toupper(c);
    }

    if (str == "TRACE")    return ARC_LOG_TRACE;
    if (str == "DEBUG")    return ARC_LOG_DEBUG;
    if (str == "INFO")     return ARC_LOG_INFO;
    if (str == "WARN" || str == "WARNING") return ARC_LOG_WARN;
    if (str == "ERROR" || str == "ERR")    return ARC_LOG_ERROR;
    if (str == "CRITICAL" || str == "CRIT") return ARC_LOG_CRITICAL;
    if (str == "OFF")      return ARC_LOG_OFF;

    return ARC_LOG_OFF;  // Default to OFF for invalid input
}

} // extern "C"