# ARC Common Logger SDK Makefile

# Compiler settings
CXX = g++
CC = gcc
AR = ar

# Directories
SPDLOG_DIR = ../Related_Project/spdlog
INC_DIR = inc
SRC_DIR = src
BUILD_DIR = build
LIB_DIR = lib

# Include paths
INCLUDES = -I$(INC_DIR) -I$(SPDLOG_DIR)/include

# Library paths
LIBPATHS = -L$(SPDLOG_DIR)/lib

# Libraries to link
LIBS = -lspdlog -lpthread

# Compiler flags
CXXFLAGS = -std=c++11 -Wall -Wextra -O2 -fPIC $(INCLUDES)
CFLAGS = -std=c99 -Wall -Wextra -O2 -fPIC $(INCLUDES)

# Debug flags (uncomment for debug build)
# CXXFLAGS += -g -DDEBUG
# CFLAGS += -g -DDEBUG

# Source files
CPP_SOURCES = $(wildcard $(SRC_DIR)/*.cpp)
C_SOURCES = $(wildcard $(SRC_DIR)/*.c)

# Object files
CPP_OBJECTS = $(CPP_SOURCES:$(SRC_DIR)/%.cpp=$(BUILD_DIR)/%.o)
C_OBJECTS = $(C_SOURCES:$(SRC_DIR)/%.c=$(BUILD_DIR)/%.o)
OBJECTS = $(CPP_OBJECTS) $(C_OBJECTS)

# Target library names
STATIC_LIB = $(LIB_DIR)/libarc_common_logger.a
SHARED_LIB = $(LIB_DIR)/libarc_common_logger.so

# Default target
all: directories $(STATIC_LIB) $(SHARED_LIB)

# Create necessary directories
directories:
	@mkdir -p $(BUILD_DIR)
	@mkdir -p $(LIB_DIR)

# Static library
$(STATIC_LIB): $(OBJECTS)
	@echo "Creating static library: $@"
	$(AR) rcs $@ $^

# Shared library
$(SHARED_LIB): $(OBJECTS)
	@echo "Creating shared library: $@"
	$(CXX) -shared -o $@ $^ $(LIBPATHS) $(LIBS)

# Compile C++ source files
$(BUILD_DIR)/%.o: $(SRC_DIR)/%.cpp
	@echo "Compiling C++: $<"
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Compile C source files
$(BUILD_DIR)/%.o: $(SRC_DIR)/%.c
	@echo "Compiling C: $<"
	$(CC) $(CFLAGS) -c $< -o $@

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)
	rm -rf $(LIB_DIR)

# Install headers and libraries (optional)
install: all
	@echo "Installing SDK..."
	@mkdir -p /usr/local/include/arc_common_logger
	@mkdir -p /usr/local/lib
	cp $(INC_DIR)/*.h /usr/local/include/arc_common_logger/
	cp $(LIB_DIR)/* /usr/local/lib/
	@echo "Installation complete"

# Uninstall
uninstall:
	@echo "Uninstalling SDK..."
	rm -rf /usr/local/include/arc_common_logger
	rm -f /usr/local/lib/libarc_common_logger.*
	@echo "Uninstallation complete"

# Test target (builds a simple test program)
test: all
	@echo "Building test program..."
	@mkdir -p $(BUILD_DIR)
	$(CXX) $(CXXFLAGS) -o $(BUILD_DIR)/test_logger test/test_logger.cpp -L$(LIB_DIR) -larc_common_logger $(LIBPATHS) $(LIBS)
	@echo "Test program built: $(BUILD_DIR)/test_logger"

# Examples target (builds example programs)
examples: all
	@echo "Building example programs..."
	@mkdir -p $(BUILD_DIR)
	$(CC) $(CFLAGS) -o $(BUILD_DIR)/simple_example examples/simple_example.c -L$(LIB_DIR) -larc_common_logger $(LIBPATHS) $(LIBS)
	$(CXX) $(CXXFLAGS) -o $(BUILD_DIR)/advanced_example examples/advanced_example.cpp -L$(LIB_DIR) -larc_common_logger $(LIBPATHS) $(LIBS)
	@echo "Example programs built:"
	@echo "  - $(BUILD_DIR)/simple_example"
	@echo "  - $(BUILD_DIR)/advanced_example"

# Help target
help:
	@echo "Available targets:"
	@echo "  all       - Build both static and shared libraries (default)"
	@echo "  clean     - Remove all build artifacts"
	@echo "  install   - Install headers and libraries to system directories"
	@echo "  uninstall - Remove installed files from system directories"
	@echo "  test      - Build test program (requires test/test_logger.cpp)"
	@echo "  examples  - Build example programs"
	@echo "  help      - Show this help message"
	@echo ""
	@echo "Build configuration:"
	@echo "  CXX=$(CXX)"
	@echo "  CXXFLAGS=$(CXXFLAGS)"
	@echo "  SPDLOG_DIR=$(SPDLOG_DIR)"

# Phony targets
.PHONY: all clean install uninstall test examples help directories

# Dependencies (automatically generated)
-include $(OBJECTS:.o=.d)