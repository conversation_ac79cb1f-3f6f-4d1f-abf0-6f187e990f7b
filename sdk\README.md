# ARC Common Logger SDK

A thread-safe logging library based on spdlog with C-style API.

## Features

- **Rotating File Sink**: Automatic log file rotation based on size
- **Async Logging**: Optional asynchronous logging for high performance
- **Pattern Syntax**: Customizable log message formatting
- **Thread Safety**: Safe to use from multiple threads
- **C++11/14 Compatible**: Uses minimal modern C++ features
- **C API**: Simple C-style interface for easy integration

## Building

### Prerequisites

- C++11 compatible compiler (GCC 4.8+, Clang 3.3+, MSVC 2013+)
- spdlog library (included in Related_Project/spdlog)
- pthread library (Linux/Unix)

### Compilation

```bash
cd sdk
make all
```

This will create:
- `lib/libarc_common_logger.a` (static library)
- `lib/libarc_common_logger.so` (shared library)

### Available Make Targets

- `make all` - Build both static and shared libraries (default)
- `make clean` - Remove all build artifacts
- `make test` - Build and run test program
- `make install` - Install to system directories
- `make help` - Show help message

## Usage

### Basic Usage

```c
#include "arc_common_logger.h"

int main() {
    // Initialize with default settings
    if (arc_logger_init_default("app.log") != ARC_LOG_SUCCESS) {
        return -1;
    }
    
    // Log messages
    arc_log_info("Application started");
    arc_log_warn("This is a warning: %d", 42);
    arc_log_error("Error occurred: %s", "file not found");
    
    // Cleanup
    arc_logger_shutdown();
    return 0;
}
```

### Advanced Configuration

```c
#include "arc_common_logger.h"

int main() {
    arc_logger_config_t config;
    arc_logger_get_default_config(&config);
    
    // Customize configuration
    config.log_file_path = "myapp.log";
    config.max_file_size = 10 * 1024 * 1024;  // 10MB
    config.max_files = 5;                      // Keep 5 rotated files
    config.async_mode = 1;                     // Enable async logging
    config.level = ARC_LOG_DEBUG;              // Set minimum log level
    config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%t] %v";
    
    if (arc_logger_init(&config) != ARC_LOG_SUCCESS) {
        return -1;
    }
    
    // Your application code here
    
    arc_logger_shutdown();
    return 0;
}
```

## API Reference

### Initialization Functions

- `arc_logger_init(config)` - Initialize with custom configuration
- `arc_logger_init_default(path)` - Initialize with default settings
- `arc_logger_shutdown()` - Cleanup and shutdown logger
- `arc_logger_is_initialized()` - Check if logger is initialized

### Configuration Functions

- `arc_logger_set_level(level)` - Set minimum log level
- `arc_logger_get_level()` - Get current log level
- `arc_logger_set_pattern(pattern)` - Set log message pattern
- `arc_logger_flush()` - Force flush pending messages

### Logging Functions

- `arc_log(level, format, ...)` - Log with specified level
- `arc_log_trace(format, ...)` - Log trace message
- `arc_log_debug(format, ...)` - Log debug message
- `arc_log_info(format, ...)` - Log info message
- `arc_log_warn(format, ...)` - Log warning message
- `arc_log_error(format, ...)` - Log error message
- `arc_log_critical(format, ...)` - Log critical message

### Utility Functions

- `arc_logger_get_default_config(config)` - Get default configuration
- `arc_log_level_to_string(level)` - Convert level to string
- `arc_log_level_from_string(str)` - Convert string to level

## Log Levels

- `ARC_LOG_TRACE` - Detailed trace information
- `ARC_LOG_DEBUG` - Debug information
- `ARC_LOG_INFO` - General information
- `ARC_LOG_WARN` - Warning messages
- `ARC_LOG_ERROR` - Error messages
- `ARC_LOG_CRITICAL` - Critical error messages
- `ARC_LOG_OFF` - Disable logging

## Pattern Syntax

The logger supports spdlog pattern syntax:

- `%Y` - Year (4 digits)
- `%m` - Month (01-12)
- `%d` - Day (01-31)
- `%H` - Hour (00-23)
- `%M` - Minute (00-59)
- `%S` - Second (00-59)
- `%e` - Milliseconds (000-999)
- `%l` - Log level
- `%^` - Start color range
- `%$` - End color range
- `%t` - Thread ID
- `%v` - Log message
- `%n` - Logger name

Default pattern: `[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v`

## Configuration Options

```c
typedef struct {
    const char* log_file_path;      // Path to log file
    unsigned long max_file_size;    // Max file size before rotation (bytes)
    unsigned int max_files;         // Number of rotated files to keep
    int async_mode;                 // 0=sync, 1=async
    const char* pattern;            // Log pattern (NULL for default)
    arc_log_level_t level;          // Minimum log level
    unsigned int async_queue_size;  // Async queue size
    unsigned int async_thread_count; // Number of async threads
} arc_logger_config_t;
```

## Thread Safety

The SDK is fully thread-safe. Multiple threads can log simultaneously without additional synchronization.

## Error Handling

All functions return `arc_log_result_t`:

- `ARC_LOG_SUCCESS` - Operation successful
- `ARC_LOG_ERROR_INVALID_PARAM` - Invalid parameter
- `ARC_LOG_ERROR_INIT_FAILED` - Initialization failed
- `ARC_LOG_ERROR_NOT_INITIALIZED` - Logger not initialized
- `ARC_LOG_ERROR_ALREADY_INITIALIZED` - Logger already initialized

## Testing

Run the test program:

```bash
make test
./build/test_logger
```

This will create several test log files demonstrating different features.

## License

This SDK is based on spdlog library. Please refer to spdlog's license for details.
